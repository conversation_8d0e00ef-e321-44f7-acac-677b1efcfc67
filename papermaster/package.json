{"name": "papermaster", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@ai-sdk/google": "^1.2.22", "@auth/drizzle-adapter": "^1.10.0", "@types/bcryptjs": "^3.0.0", "@types/pg": "^8.15.4", "@types/uuid": "^10.0.0", "ai": "^4.3.19", "bcryptjs": "^3.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "drizzle-kit": "^0.31.4", "drizzle-orm": "^0.44.3", "lucide-react": "^0.525.0", "next": "15.4.1", "next-auth": "^4.24.11", "pg": "^8.16.3", "postgres": "^3.4.7", "react": "19.1.0", "react-dom": "19.1.0", "tailwind-merge": "^3.3.1", "uuid": "^11.1.0", "zod": "^3.25.76"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.4.1", "tailwindcss": "^4", "typescript": "^5"}}