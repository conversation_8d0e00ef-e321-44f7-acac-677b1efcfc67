import { streamText } from 'ai'
import { google } from '@ai-sdk/google'
import { NextRequest } from 'next/server'
import { generateAcademicSystemPrompt } from '@/lib/ai-config'
import { AcademicLevel, Discipline } from '@/lib/types'

export async function POST(req: NextRequest) {
  try {
    const { messages, academicLevel, discipline, documentContext } = await req.json()

    // 验证必需参数
    if (!messages || !academicLevel || !discipline) {
      return new Response('Missing required parameters', { status: 400 })
    }

    // 生成系统提示
    const systemPrompt = generateAcademicSystemPrompt(
      academicLevel as AcademicLevel,
      discipline as Discipline
    )

    // 添加文档上下文到系统提示
    const enhancedSystemPrompt = documentContext 
      ? `${systemPrompt}\n\n当前文档上下文：\n${documentContext}`
      : systemPrompt

    // 配置AI模型
    const model = google('gemini-2.0-flash-exp')

    // 生成流式响应
    const result = streamText({
      model,
      system: enhancedSystemPrompt,
      messages,
      temperature: 0.7,
      maxTokens: 2000,
    })

    return result.toDataStreamResponse()
  } catch (error) {
    console.error('Chat API error:', error)
    return new Response('Internal server error', { status: 500 })
  }
}
