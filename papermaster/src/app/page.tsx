"use client";

import { DISCIPLINE_CONFIG } from "@/lib/academic-config";
import { AcademicLevel, Discipline } from "@/lib/types";
import { useChat } from "ai/react";
import { useState } from "react";

export default function Home() {
	const [academicLevel, setAcademicLevel] = useState<AcademicLevel>("masters");
	const [discipline, setDiscipline] = useState<Discipline>("computer_science");

	const { messages, input, handleInputChange, handleSubmit, isLoading } =
		useChat({
			api: "/api/chat",
			body: {
				academicLevel,
				discipline,
			},
		});

	return (
		<div className="min-h-screen bg-gray-50 py-8">
			<div className="max-w-4xl mx-auto px-4">
				<header className="text-center mb-8">
					<h1 className="text-3xl font-bold text-gray-900 mb-2">
						PaperMaster - AI论文开题报告写作助手
					</h1>
					<p className="text-gray-600">
						基于AI的智能学术写作平台，助力您完成高质量的开题报告
					</p>
				</header>

				{/* 配置面板 */}
				<div className="bg-white rounded-lg shadow-md p-6 mb-6">
					<h2 className="text-lg font-semibold mb-4">学术配置</h2>
					<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								学术层次
							</label>
							<select
								value={academicLevel}
								onChange={(e) =>
									setAcademicLevel(e.target.value as AcademicLevel)
								}
								className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							>
								<option value="undergraduate">本科生</option>
								<option value="masters">硕士研究生</option>
								<option value="doctoral">博士研究生</option>
							</select>
						</div>
						<div>
							<label className="block text-sm font-medium text-gray-700 mb-2">
								学科领域
							</label>
							<select
								value={discipline}
								onChange={(e) => setDiscipline(e.target.value as Discipline)}
								className="w-full p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
							>
								{Object.entries(DISCIPLINE_CONFIG).map(([key, config]) => (
									<option key={key} value={key}>
										{config.name}
									</option>
								))}
							</select>
						</div>
					</div>
				</div>

				{/* 聊天界面 */}
				<div className="bg-white rounded-lg shadow-md">
					<div className="p-4 border-b border-gray-200">
						<h2 className="text-lg font-semibold">AI写作助手</h2>
					</div>

					<div className="h-96 overflow-y-auto p-4 space-y-4">
						{messages.length === 0 && (
							<div className="text-center text-gray-500 py-8">
								<p>欢迎使用AI论文写作助手！</p>
								<p>请输入您的问题或需要帮助的内容。</p>
							</div>
						)}

						{messages.map((message) => (
							<div
								key={message.id}
								className={`flex ${
									message.role === "user" ? "justify-end" : "justify-start"
								}`}
							>
								<div
									className={`max-w-xs lg:max-w-md px-4 py-2 rounded-lg ${
										message.role === "user"
											? "bg-blue-500 text-white"
											: "bg-gray-200 text-gray-800"
									}`}
								>
									<p className="whitespace-pre-wrap">{message.content}</p>
								</div>
							</div>
						))}

						{isLoading && (
							<div className="flex justify-start">
								<div className="bg-gray-200 text-gray-800 px-4 py-2 rounded-lg">
									<p>AI正在思考中...</p>
								</div>
							</div>
						)}
					</div>

					<form
						onSubmit={handleSubmit}
						className="p-4 border-t border-gray-200"
					>
						<div className="flex space-x-2">
							<input
								value={input}
								onChange={handleInputChange}
								placeholder="请输入您的问题..."
								className="flex-1 p-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
								disabled={isLoading}
							/>
							<button
								type="submit"
								disabled={isLoading}
								className="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
							>
								发送
							</button>
						</div>
					</form>
				</div>
			</div>
		</div>
	);
}
