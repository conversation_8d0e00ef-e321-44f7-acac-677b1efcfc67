import NextAuth from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import { DrizzleAdapter } from '@auth/drizzle-adapter'
import { db } from '@/lib/db'
import { users } from '@/lib/db/schema'
import { eq } from 'drizzle-orm'
import bcrypt from 'bcryptjs'
import { z } from 'zod'

// 登录表单验证schema
const loginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少需要6个字符'),
})

// 注册表单验证schema
const registerSchema = z.object({
  name: z.string().min(2, '姓名至少需要2个字符'),
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(6, '密码至少需要6个字符'),
  institution: z.string().optional(),
  academicLevel: z.enum(['undergraduate', 'masters', 'doctoral']),
  discipline: z.string().min(1, '请选择学科领域'),
})

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: DrizzleAdapter(db),
  session: {
    strategy: 'jwt',
  },
  pages: {
    signIn: '/auth/signin',
    signUp: '/auth/signup',
  },
  providers: [
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        try {
          const { email, password } = loginSchema.parse(credentials)

          // 查找用户
          const [user] = await db
            .select()
            .from(users)
            .where(eq(users.email, email))

          if (!user) {
            return null
          }

          // 验证密码（这里假设密码已经加密存储）
          // 在实际应用中，你需要在注册时加密密码
          const isValidPassword = await bcrypt.compare(password, user.password || '')

          if (!isValidPassword) {
            return null
          }

          return {
            id: user.id,
            email: user.email,
            name: user.name,
            institution: user.institution,
            academicLevel: user.academicLevel,
            discipline: user.discipline,
          }
        } catch (error) {
          console.error('Authentication error:', error)
          return null
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.id = user.id
        token.academicLevel = user.academicLevel
        token.discipline = user.discipline
        token.institution = user.institution
      }
      return token
    },
    async session({ session, token }) {
      if (token) {
        session.user.id = token.id as string
        session.user.academicLevel = token.academicLevel as string
        session.user.discipline = token.discipline as string
        session.user.institution = token.institution as string
      }
      return session
    },
  },
})

// 用户注册函数
export async function registerUser(data: z.infer<typeof registerSchema>) {
  try {
    const validatedData = registerSchema.parse(data)

    // 检查用户是否已存在
    const [existingUser] = await db
      .select()
      .from(users)
      .where(eq(users.email, validatedData.email))

    if (existingUser) {
      throw new Error('用户已存在')
    }

    // 加密密码
    const hashedPassword = await bcrypt.hash(validatedData.password, 12)

    // 创建用户
    const [newUser] = await db
      .insert(users)
      .values({
        name: validatedData.name,
        email: validatedData.email,
        password: hashedPassword,
        institution: validatedData.institution,
        academicLevel: validatedData.academicLevel,
        discipline: validatedData.discipline,
      })
      .returning()

    return {
      success: true,
      user: {
        id: newUser.id,
        email: newUser.email,
        name: newUser.name,
      },
    }
  } catch (error) {
    console.error('Registration error:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : '注册失败',
    }
  }
}

// 获取当前用户信息
export async function getCurrentUser() {
  const session = await auth()
  return session?.user
}

// 检查用户是否已认证
export async function requireAuth() {
  const user = await getCurrentUser()
  if (!user) {
    throw new Error('未认证')
  }
  return user
}
