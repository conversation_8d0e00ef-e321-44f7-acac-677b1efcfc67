import { google } from '@ai-sdk/google'
import { AcademicLevel, Discipline } from './types'

// AI模型配置
export const AI_MODELS = {
  GEMINI_FLASH: 'gemini-2.0-flash-exp',
  GEMINI_PRO: 'gemini-1.5-pro'
} as const

// 获取AI模型实例
export function getAIModel(modelName: keyof typeof AI_MODELS) {
  return google(AI_MODELS[modelName])
}

// 生成学术写作系统提示
export function generateAcademicSystemPrompt(
  academicLevel: AcademicLevel,
  discipline: Discipline
): string {
  const levelDescriptions = {
    undergraduate: '本科生',
    masters: '硕士研究生',
    doctoral: '博士研究生'
  }

  return `你是一个专业的学术写作助手，专门协助${levelDescriptions[academicLevel]}完成${discipline}领域的论文开题报告写作。

你的职责包括：
1. 提供专业的学术写作指导和建议
2. 协助完善研究思路和论证逻辑
3. 帮助规范学术表达和格式
4. 提供相关的研究方法建议
5. 协助文献综述和引用管理

请始终遵循以下原则：
- 确保学术诚信，不提供抄袭内容
- 提供原创性的思路和建议
- 符合${academicLevel}层次的学术要求
- 遵循${discipline}领域的学术规范
- 鼓励批判性思维和创新性研究

在回答时请：
- 使用专业但易懂的学术语言
- 提供具体可行的建议
- 引导用户独立思考
- 强调学术规范的重要性`
}

// AI工具配置
export const AI_TOOLS = {
  structureAnalysis: {
    description: '分析文档结构和学术规范符合度',
    parameters: {
      type: 'object',
      properties: {
        content: {
          type: 'string',
          description: '要分析的文档内容'
        },
        targetStructure: {
          type: 'string',
          description: '目标结构要求'
        }
      },
      required: ['content', 'targetStructure']
    }
  },
  citationAssistance: {
    description: '协助引用格式化和文献管理',
    parameters: {
      type: 'object',
      properties: {
        references: {
          type: 'array',
          items: { type: 'string' },
          description: '参考文献列表'
        },
        style: {
          type: 'string',
          enum: ['apa', 'mla', 'chicago', 'ieee', 'harvard'],
          description: '引用格式'
        }
      },
      required: ['references', 'style']
    }
  },
  researchSuggestion: {
    description: '提供研究方法和思路建议',
    parameters: {
      type: 'object',
      properties: {
        topic: {
          type: 'string',
          description: '研究主题'
        },
        discipline: {
          type: 'string',
          description: '学科领域'
        },
        academicLevel: {
          type: 'string',
          description: '学术层次'
        }
      },
      required: ['topic', 'discipline', 'academicLevel']
    }
  }
}

// 生成内容的默认配置
export const GENERATION_CONFIG = {
  temperature: 0.7,
  maxTokens: 2000,
  topP: 0.9,
  frequencyPenalty: 0.1,
  presencePenalty: 0.1
}
