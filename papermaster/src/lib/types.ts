// 学术层次类型
export type AcademicLevel = 'undergraduate' | 'masters' | 'doctoral'

// 学科类型
export type Discipline = 
  | 'computer_science'
  | 'engineering'
  | 'mathematics'
  | 'physics'
  | 'chemistry'
  | 'biology'
  | 'medicine'
  | 'literature'
  | 'history'
  | 'philosophy'
  | 'economics'
  | 'psychology'
  | 'sociology'
  | 'education'
  | 'law'
  | 'business'
  | 'other'

// 文档状态
export type DocumentStatus = 'draft' | 'in_progress' | 'completed' | 'submitted'

// 学术层次配置
export interface AcademicLevelConfig {
  wordCount: [number, number]
  sections: string[]
  complexityLevel: 'basic' | 'intermediate' | 'advanced'
  mentorshipRequired: boolean
}

// 用户类型
export interface User {
  id: string
  email: string
  name: string
  institution?: string
  academicLevel: AcademicLevel
  discipline: Discipline
  createdAt: Date
  updatedAt: Date
}

// 文档类型
export interface Document {
  id: string
  userId: string
  title: string
  content: string
  documentType: 'proposal' | 'thesis' | 'paper'
  academicLevel: AcademicLevel
  discipline: Discipline
  status: DocumentStatus
  metadata: Record<string, any>
  createdAt: Date
  updatedAt: Date
}

// AI交互记录
export interface AIInteraction {
  id: string
  userId: string
  documentId?: string
  agentName: string
  inputPrompt: string
  outputContent: string
  modelUsed: string
  tokensConsumed: number
  interactionType: 'chat' | 'generation' | 'analysis' | 'suggestion'
  createdAt: Date
}

// 引用类型
export interface Citation {
  id: string
  documentId: string
  citationKey: string
  citationData: {
    title: string
    authors: string[]
    year: number
    journal?: string
    volume?: string
    pages?: string
    doi?: string
    url?: string
    type: 'article' | 'book' | 'conference' | 'website' | 'other'
  }
  styleFormat: 'apa' | 'mla' | 'chicago' | 'ieee' | 'harvard'
  createdAt: Date
}

// 模板配置
export interface TemplateConfig {
  id: string
  name: string
  academicLevel: AcademicLevel
  discipline: Discipline
  sections: TemplateSection[]
  guidelines: string[]
}

// 模板章节
export interface TemplateSection {
  id: string
  title: string
  description: string
  required: boolean
  order: number
  wordCountRange?: [number, number]
  guidelines: string[]
}
