import { AcademicLevel, AcademicLevelConfig, Discipline } from './types'

// 学术层次配置
export const ACADEMIC_LEVEL_CONFIG: Record<AcademicLevel, AcademicLevelConfig> = {
  undergraduate: {
    wordCount: [2000, 3000],
    sections: ['background', 'literature', 'methodology', 'timeline', 'references'],
    complexityLevel: 'basic',
    mentorshipRequired: false
  },
  masters: {
    wordCount: [3000, 5000],
    sections: ['background', 'literature', 'arguments', 'methodology', 'innovation', 'timeline', 'references'],
    complexityLevel: 'intermediate',
    mentorshipRequired: true
  },
  doctoral: {
    wordCount: [5000, 8000],
    sections: ['background', 'literature', 'theoretical_framework', 'methodology', 'innovation', 'feasibility', 'timeline', 'references'],
    complexityLevel: 'advanced',
    mentorshipRequired: true
  }
}

// 学科配置
export const DISCIPLINE_CONFIG: Record<Discipline, { name: string; category: string; methods: string[] }> = {
  computer_science: {
    name: '计算机科学',
    category: '理工科',
    methods: ['实验研究', '算法分析', '系统设计', '性能评估']
  },
  engineering: {
    name: '工程学',
    category: '理工科',
    methods: ['实验设计', '建模仿真', '原型开发', '性能测试']
  },
  mathematics: {
    name: '数学',
    category: '理工科',
    methods: ['理论证明', '数值分析', '建模研究', '算法设计']
  },
  physics: {
    name: '物理学',
    category: '理工科',
    methods: ['实验研究', '理论分析', '数值模拟', '数据分析']
  },
  chemistry: {
    name: '化学',
    category: '理工科',
    methods: ['实验合成', '分析测试', '机理研究', '性能评价']
  },
  biology: {
    name: '生物学',
    category: '理工科',
    methods: ['实验研究', '观察分析', '统计分析', '分子生物学技术']
  },
  medicine: {
    name: '医学',
    category: '医学',
    methods: ['临床研究', '实验研究', '流行病学调查', '病例分析']
  },
  literature: {
    name: '文学',
    category: '文科',
    methods: ['文本分析', '比较研究', '历史考证', '理论阐释']
  },
  history: {
    name: '历史学',
    category: '文科',
    methods: ['史料考证', '文献分析', '田野调查', '比较研究']
  },
  philosophy: {
    name: '哲学',
    category: '文科',
    methods: ['概念分析', '逻辑论证', '文本阐释', '思辨研究']
  },
  economics: {
    name: '经济学',
    category: '社科',
    methods: ['计量分析', '理论建模', '实证研究', '案例分析']
  },
  psychology: {
    name: '心理学',
    category: '社科',
    methods: ['实验研究', '问卷调查', '观察法', '统计分析']
  },
  sociology: {
    name: '社会学',
    category: '社科',
    methods: ['问卷调查', '深度访谈', '参与观察', '统计分析']
  },
  education: {
    name: '教育学',
    category: '社科',
    methods: ['行动研究', '案例研究', '实验研究', '调查研究']
  },
  law: {
    name: '法学',
    category: '社科',
    methods: ['案例分析', '比较研究', '实证研究', '理论分析']
  },
  business: {
    name: '商学',
    category: '社科',
    methods: ['案例研究', '实证分析', '调查研究', '建模分析']
  },
  other: {
    name: '其他',
    category: '其他',
    methods: ['根据具体学科确定']
  }
}

// 章节模板
export const SECTION_TEMPLATES = {
  background: {
    title: '研究背景',
    description: '阐述研究的背景、目的和意义',
    guidelines: [
      '明确研究问题的来源和重要性',
      '说明研究的理论意义和实践价值',
      '阐述研究的必要性和紧迫性'
    ]
  },
  literature: {
    title: '文献综述',
    description: '回顾和分析相关研究现状',
    guidelines: [
      '系统梳理国内外相关研究',
      '分析现有研究的优势和不足',
      '找出研究空白和发展趋势'
    ]
  },
  theoretical_framework: {
    title: '理论框架',
    description: '构建研究的理论基础',
    guidelines: [
      '选择和阐述相关理论',
      '构建理论分析框架',
      '说明理论与研究问题的关系'
    ]
  },
  methodology: {
    title: '研究方法',
    description: '说明研究的方法和技术路线',
    guidelines: [
      '选择合适的研究方法',
      '设计研究方案和步骤',
      '说明数据收集和分析方法'
    ]
  },
  innovation: {
    title: '创新点',
    description: '阐述研究的创新之处',
    guidelines: [
      '明确研究的创新点',
      '说明创新的理论或实践价值',
      '与现有研究进行比较'
    ]
  },
  feasibility: {
    title: '可行性分析',
    description: '分析研究的可行性',
    guidelines: [
      '分析研究条件和资源',
      '评估技术可行性',
      '识别潜在风险和对策'
    ]
  },
  timeline: {
    title: '时间安排',
    description: '制定研究的时间计划',
    guidelines: [
      '分阶段安排研究任务',
      '设定关键节点和里程碑',
      '合理分配时间和资源'
    ]
  },
  references: {
    title: '参考文献',
    description: '列出主要参考文献',
    guidelines: [
      '选择权威和最新的文献',
      '按照规范格式引用',
      '确保文献的相关性和质量'
    ]
  }
}
