import { eq, desc, and } from 'drizzle-orm'
import { db } from './index'
import { users, documents, aiInteractions, citations, templates, collaborations } from './schema'
import type { 
  NewUser, 
  NewDocument, 
  NewAIInteraction, 
  NewCitation,
  AcademicLevel,
  Discipline 
} from '@/lib/types'

// 用户相关操作
export async function createUser(userData: NewUser) {
  const [user] = await db.insert(users).values(userData).returning()
  return user
}

export async function getUserById(id: string) {
  const [user] = await db.select().from(users).where(eq(users.id, id))
  return user
}

export async function getUserByEmail(email: string) {
  const [user] = await db.select().from(users).where(eq(users.email, email))
  return user
}

export async function updateUser(id: string, userData: Partial<NewUser>) {
  const [user] = await db
    .update(users)
    .set({ ...userData, updatedAt: new Date() })
    .where(eq(users.id, id))
    .returning()
  return user
}

// 文档相关操作
export async function createDocument(documentData: NewDocument) {
  const [document] = await db.insert(documents).values(documentData).returning()
  return document
}

export async function getDocumentById(id: string) {
  const [document] = await db.select().from(documents).where(eq(documents.id, id))
  return document
}

export async function getDocumentsByUserId(userId: string) {
  return await db
    .select()
    .from(documents)
    .where(eq(documents.userId, userId))
    .orderBy(desc(documents.updatedAt))
}

export async function updateDocument(id: string, documentData: Partial<NewDocument>) {
  const [document] = await db
    .update(documents)
    .set({ ...documentData, updatedAt: new Date() })
    .where(eq(documents.id, id))
    .returning()
  return document
}

export async function deleteDocument(id: string) {
  await db.delete(documents).where(eq(documents.id, id))
}

// AI交互相关操作
export async function createAIInteraction(interactionData: NewAIInteraction) {
  const [interaction] = await db.insert(aiInteractions).values(interactionData).returning()
  return interaction
}

export async function getAIInteractionsByUserId(userId: string, limit = 50) {
  return await db
    .select()
    .from(aiInteractions)
    .where(eq(aiInteractions.userId, userId))
    .orderBy(desc(aiInteractions.createdAt))
    .limit(limit)
}

export async function getAIInteractionsByDocumentId(documentId: string) {
  return await db
    .select()
    .from(aiInteractions)
    .where(eq(aiInteractions.documentId, documentId))
    .orderBy(desc(aiInteractions.createdAt))
}

// 引用相关操作
export async function createCitation(citationData: NewCitation) {
  const [citation] = await db.insert(citations).values(citationData).returning()
  return citation
}

export async function getCitationsByDocumentId(documentId: string) {
  return await db
    .select()
    .from(citations)
    .where(eq(citations.documentId, documentId))
    .orderBy(desc(citations.createdAt))
}

export async function updateCitation(id: string, citationData: Partial<NewCitation>) {
  const [citation] = await db
    .update(citations)
    .set(citationData)
    .where(eq(citations.id, id))
    .returning()
  return citation
}

export async function deleteCitation(id: string) {
  await db.delete(citations).where(eq(citations.id, id))
}

// 模板相关操作
export async function getTemplatesByAcademicLevel(
  academicLevel: AcademicLevel,
  discipline?: Discipline
) {
  const conditions = [eq(templates.academicLevel, academicLevel), eq(templates.isActive, true)]
  
  if (discipline) {
    conditions.push(eq(templates.discipline, discipline))
  }

  return await db
    .select()
    .from(templates)
    .where(and(...conditions))
    .orderBy(templates.name)
}

export async function getTemplateById(id: string) {
  const [template] = await db.select().from(templates).where(eq(templates.id, id))
  return template
}

// 协作相关操作
export async function addCollaborator(documentId: string, userId: string, role: string = 'viewer') {
  const [collaboration] = await db
    .insert(collaborations)
    .values({ documentId, userId, role })
    .returning()
  return collaboration
}

export async function getDocumentCollaborators(documentId: string) {
  return await db
    .select({
      id: collaborations.id,
      role: collaborations.role,
      permissions: collaborations.permissions,
      createdAt: collaborations.createdAt,
      user: {
        id: users.id,
        name: users.name,
        email: users.email,
      }
    })
    .from(collaborations)
    .innerJoin(users, eq(collaborations.userId, users.id))
    .where(eq(collaborations.documentId, documentId))
}

export async function removeCollaborator(documentId: string, userId: string) {
  await db
    .delete(collaborations)
    .where(and(
      eq(collaborations.documentId, documentId),
      eq(collaborations.userId, userId)
    ))
}

// 统计相关操作
export async function getUserStats(userId: string) {
  const [documentCount] = await db
    .select({ count: documents.id })
    .from(documents)
    .where(eq(documents.userId, userId))

  const [interactionCount] = await db
    .select({ count: aiInteractions.id })
    .from(aiInteractions)
    .where(eq(aiInteractions.userId, userId))

  return {
    documentCount: documentCount?.count || 0,
    interactionCount: interactionCount?.count || 0,
  }
}
