import { drizzle } from 'drizzle-orm/postgres-js'
import postgres from 'postgres'
import * as schema from './schema'

// 数据库连接配置
const connectionString = process.env.DATABASE_URL || process.env.POSTGRES_URL

if (!connectionString) {
  throw new Error('DATABASE_URL or POSTGRES_URL environment variable is required')
}

// 创建数据库连接
const client = postgres(connectionString, {
  max: 10,
  idle_timeout: 20,
  connect_timeout: 10,
})

// 创建Drizzle实例
export const db = drizzle(client, { schema })

// 导出schema以便在其他地方使用
export * from './schema'

// 数据库健康检查
export async function checkDatabaseConnection() {
  try {
    await client`SELECT 1`
    return { status: 'connected', message: 'Database connection successful' }
  } catch (error) {
    console.error('Database connection failed:', error)
    return { status: 'error', message: 'Database connection failed', error }
  }
}

// 关闭数据库连接
export async function closeDatabaseConnection() {
  await client.end()
}
