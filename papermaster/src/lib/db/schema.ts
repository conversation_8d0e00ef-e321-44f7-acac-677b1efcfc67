import { relations } from "drizzle-orm";
import {
	boolean,
	integer,
	jsonb,
	pgTable,
	text,
	timestamp,
	uuid,
	varchar,
} from "drizzle-orm/pg-core";

// 用户表
export const users = pgTable("users", {
	id: uuid("id").primaryKey().defaultRandom(),
	email: varchar("email", { length: 255 }).notNull().unique(),
	name: varchar("name", { length: 255 }).notNull(),
	password: varchar("password", { length: 255 }),
	institution: varchar("institution", { length: 255 }),
	academicLevel: varchar("academic_level", { length: 50 }).notNull(),
	discipline: varchar("discipline", { length: 100 }).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// 文档表
export const documents = pgTable("documents", {
	id: uuid("id").primary<PERSON>ey().defaultRandom(),
	userId: uuid("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	title: varchar("title", { length: 500 }).notNull(),
	content: text("content"),
	documentType: varchar("document_type", { length: 50 })
		.default("proposal")
		.notNull(),
	academicLevel: varchar("academic_level", { length: 50 }).notNull(),
	discipline: varchar("discipline", { length: 100 }).notNull(),
	status: varchar("status", { length: 20 }).default("draft").notNull(),
	metadata: jsonb("metadata"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// AI交互记录表
export const aiInteractions = pgTable("ai_interactions", {
	id: uuid("id").primaryKey().defaultRandom(),
	userId: uuid("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	documentId: uuid("document_id").references(() => documents.id, {
		onDelete: "cascade",
	}),
	agentName: varchar("agent_name", { length: 100 }),
	inputPrompt: text("input_prompt").notNull(),
	outputContent: text("output_content").notNull(),
	modelUsed: varchar("model_used", { length: 100 }).notNull(),
	tokensConsumed: integer("tokens_consumed"),
	interactionType: varchar("interaction_type", { length: 50 }).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// 引用表
export const citations = pgTable("citations", {
	id: uuid("id").primaryKey().defaultRandom(),
	documentId: uuid("document_id")
		.notNull()
		.references(() => documents.id, { onDelete: "cascade" }),
	citationKey: varchar("citation_key", { length: 100 }).notNull(),
	citationData: jsonb("citation_data").notNull(),
	styleFormat: varchar("style_format", { length: 50 }).default("apa").notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// 模板表
export const templates = pgTable("templates", {
	id: uuid("id").primaryKey().defaultRandom(),
	name: varchar("name", { length: 255 }).notNull(),
	academicLevel: varchar("academic_level", { length: 50 }).notNull(),
	discipline: varchar("discipline", { length: 100 }).notNull(),
	sections: jsonb("sections").notNull(),
	guidelines: jsonb("guidelines").notNull(),
	isActive: boolean("is_active").default(true).notNull(),
	createdAt: timestamp("created_at").defaultNow().notNull(),
	updatedAt: timestamp("updated_at").defaultNow().notNull(),
});

// 协作表
export const collaborations = pgTable("collaborations", {
	id: uuid("id").primaryKey().defaultRandom(),
	documentId: uuid("document_id")
		.notNull()
		.references(() => documents.id, { onDelete: "cascade" }),
	userId: uuid("user_id")
		.notNull()
		.references(() => users.id, { onDelete: "cascade" }),
	role: varchar("role", { length: 50 }).default("viewer").notNull(), // owner, editor, viewer
	permissions: jsonb("permissions"),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// 版本控制表
export const documentVersions = pgTable("document_versions", {
	id: uuid("id").primaryKey().defaultRandom(),
	documentId: uuid("document_id")
		.notNull()
		.references(() => documents.id, { onDelete: "cascade" }),
	version: integer("version").notNull(),
	content: text("content").notNull(),
	changes: jsonb("changes"),
	createdBy: uuid("created_by")
		.notNull()
		.references(() => users.id),
	createdAt: timestamp("created_at").defaultNow().notNull(),
});

// 关系定义
export const usersRelations = relations(users, ({ many }) => ({
	documents: many(documents),
	aiInteractions: many(aiInteractions),
	collaborations: many(collaborations),
	documentVersions: many(documentVersions),
}));

export const documentsRelations = relations(documents, ({ one, many }) => ({
	user: one(users, {
		fields: [documents.userId],
		references: [users.id],
	}),
	citations: many(citations),
	aiInteractions: many(aiInteractions),
	collaborations: many(collaborations),
	versions: many(documentVersions),
}));

export const aiInteractionsRelations = relations(aiInteractions, ({ one }) => ({
	user: one(users, {
		fields: [aiInteractions.userId],
		references: [users.id],
	}),
	document: one(documents, {
		fields: [aiInteractions.documentId],
		references: [documents.id],
	}),
}));

export const citationsRelations = relations(citations, ({ one }) => ({
	document: one(documents, {
		fields: [citations.documentId],
		references: [documents.id],
	}),
}));

export const collaborationsRelations = relations(collaborations, ({ one }) => ({
	document: one(documents, {
		fields: [collaborations.documentId],
		references: [documents.id],
	}),
	user: one(users, {
		fields: [collaborations.userId],
		references: [users.id],
	}),
}));

export const documentVersionsRelations = relations(
	documentVersions,
	({ one }) => ({
		document: one(documents, {
			fields: [documentVersions.documentId],
			references: [documents.id],
		}),
		createdBy: one(users, {
			fields: [documentVersions.createdBy],
			references: [users.id],
		}),
	}),
);

// 导出类型
export type User = typeof users.$inferSelect;
export type NewUser = typeof users.$inferInsert;
export type Document = typeof documents.$inferSelect;
export type NewDocument = typeof documents.$inferInsert;
export type AIInteraction = typeof aiInteractions.$inferSelect;
export type NewAIInteraction = typeof aiInteractions.$inferInsert;
export type Citation = typeof citations.$inferSelect;
export type NewCitation = typeof citations.$inferInsert;
export type Template = typeof templates.$inferSelect;
export type NewTemplate = typeof templates.$inferInsert;
